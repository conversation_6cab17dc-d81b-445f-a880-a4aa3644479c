<template>
    <ui-view
        type="form"
        ref="view"
        collection="finance.entries"
        method="finance.save-entry"
        :model="model"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :assignation="assignation"
        :activity-payload="activityPayload"
        :actions="actions"
        :extra-actions="extraActions"
        print-method="finance.print-entry"
        @changed="handleChange"
        v-if="initialized"
    >
        <template slot="form-top" v-if="!$params('saveAsApproved')">
            <ui-status :statuses="statuses" :value="status" />

            <ui-related-documents :documents="model.relatedDocuments" />

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-thumbs-up"
                :disabled="
                    !$params('isPreview') || !$params('id') || !model.status || status !== 'draft' || $params('loading')
                "
                v-show="!$params('inDialog')"
                @click="handleApprove"
            >
                {{ 'Approve' | t }}
            </el-button>
        </template>

        <div class="columns">
            <div class="column is-half">
                <ui-legend title="General" />
                <ui-field name="code" disabled v-show="!!model.code" />
                <ui-field
                    name="type"
                    :options="typeOptions"
                    translate-labels
                    v-show="!$params('type')"
                    :disabled="status !== 'draft'"
                />
                <div class="ui-inline-fields">
                    <div class="field-label">{{ 'Document type' | t }}</div>
                    <div class="field-content">
                        <ui-field
                            name="documentType"
                            :options="documentTypeOptions"
                            translate-labels
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :disabled="status !== 'draft' || !!$params('documentType')"
                        />
                        <ui-field name="no" label="hide" :style="{flex: '0 0 75px'}" />
                    </div>
                </div>
                <ui-field
                    name="partnerType"
                    :options="partnerTypeOptions"
                    translate-labels
                    :disabled="model.endorse || status !== 'draft' || !!$params('model.partnerType')"
                />
                <kernel-common-partner-select
                    :key="partnerIdKey"
                    name="partnerId"
                    :label="partnerLabel"
                    :filters="{type: model.partnerType}"
                    :update-params="updatePartnerIdParams"
                    :disabled="
                        model.endorse || status !== 'draft' || !model.partnerType || !!$params('model.partnerType')
                    "
                />
                <ui-field
                    name="contactPersonId"
                    collection="kernel.contacts"
                    view="partners.contacts"
                    :filters="{partnerId: model.partnerId, type: 'contact'}"
                    :update-params="updateContactPersonIdIdParams"
                    :disabled="model.endorse || !model.partnerId || status !== 'draft'"
                />
                <div class="ui-inline-fields" v-show="!$params('documentType')">
                    <div class="field-label">{{ 'Account' | t }}</div>
                    <div class="field-content">
                        <ui-field
                            name="journalId"
                            v-bind="journalIdAttributes"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :disabled="model.endorse || status !== 'draft'"
                            v-show="model.documentType !== 'pos' && model.documentType !== 'creditCard'"
                        />
                        <ui-field
                            name="posId"
                            collection="accounting.pos"
                            view="accounting.configuration.pos"
                            disable-create
                            disable-detail
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :filters="posFilters"
                            :disabled="status !== 'draft'"
                            v-show="model.documentType === 'pos'"
                        />
                        <ui-field
                            name="creditCardId"
                            collection="accounting.credit-cards"
                            view="accounting.configuration.credit-cards"
                            disable-create
                            disable-detail
                            :filters="creditCardFilters"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :disabled="status !== 'draft'"
                            v-show="model.documentType === 'creditCard'"
                        />
                        <ui-field
                            name="currencyId"
                            collection="kernel.currencies"
                            :style="{flex: '0 0 90px'}"
                            label="hide"
                        />
                        <ui-field
                            name="currencyRate"
                            :style="{flex: '0 0 80px'}"
                            :precision="$setting('system.exchangeRatePrecision')"
                            label="hide"
                            v-show="isForeignCurrency"
                        />
                    </div>
                </div>
                <ui-field
                    name="selectedCardBrand"
                    collection="finance.card-brands"
                    view="finance.configuration.card-brands"
                    value-from="code"
                    label-from="name"
                    disable-detail
                    disable-create
                    v-show="isSelectedCardBrandShown"
                />
                <ui-field name="documentNo" :disabled="model.endorse" />
                <ui-field
                    name="chequeTrackingCode"
                    :disabled="true"
                    v-show="model.status === 'approved' && model.documentType === 'cheque'"
                />
                <ui-field
                    name="guaranteeId"
                    collection="finance.guarantees"
                    view="finance.banking.guarantees.list"
                    :filters="{
                        type: {$in: ['partner-received', 'partner-issued']},
                        partnerId: model.partnerId,
                        status: 'approved'
                    }"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    disable-detail
                    disable-create
                    :disabled="status !== 'draft' || !model.partnerId"
                />
                <ui-field
                    name="paymentAccountId"
                    collection="accounting.journals"
                    view="accounting.configuration.journals"
                    :filters="paymentAccountIdFilters"
                    disable-detail
                    disable-create
                    :disabled="model.endorse || status !== 'draft'"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="endorsable"
                    :disabled="status !== 'draft'"
                    v-show="(model.documentType === 'cheque' || model.documentType === 'promissoryNote') && isReceipt"
                />
                <ui-field
                    name="original"
                    :disabled="status !== 'draft'"
                    v-show="(model.documentType === 'cheque' || model.documentType === 'promissoryNote') && isReceipt"
                />
                <ui-field
                    name="endorse"
                    :disabled="status !== 'draft'"
                    v-show="(model.documentType === 'cheque' || model.documentType === 'promissoryNote') && isPayment"
                />
                <ui-field
                    name="chequeIdToEndorse"
                    collection="finance.cheques"
                    view="finance.banking.cheque.list"
                    :filters="chequeIdToEndorseFilters"
                    disable-detail
                    disable-create
                    value-from="_id"
                    label-from="chequeNumber"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'cheque' && model.endorse && isPayment"
                />
                <ui-field
                    name="promissoryNoteIdToEndorse"
                    collection="finance.promissory-notes"
                    view="finance.banking.promissory-note.list"
                    :filters="promissoryNoteIdToEndorseFilters"
                    disable-detail
                    disable-create
                    value-from="_id"
                    label-from="promissoryNoteNumber"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'promissoryNote' && model.endorse && isPayment"
                />
                <ui-field name="cashFlowItemId" v-bind="cashFlowItemIdAttributes" :disabled="model.endorse" />
                <ui-field
                    name="financialProjectId"
                    collection="kernel.financial-projects"
                    view="system.management.configuration.financial-projects"
                    :filters="financialProjectIdFilters"
                    disable-detail
                    disable-create
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :disabled="model.endorse"
                />

                <ui-legend :title="isReceipt ? 'Receipt' : 'Payment'" class="mt30" />
                <ui-field
                    name="amount"
                    :precision="$setting('system.currencyPrecision')"
                    :disabled="model.endorse || status !== 'draft'"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="systemCurrencyTotal"
                    :label="$t('Amount (SC)')"
                    :precision="$setting('system.currencyPrecision')"
                    v-show="!!model.currencyId && model.currencyId !== systemCurrencyId"
                >
                    <div :slot="systemCurrencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ systemCurrencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field name="calculateDueDifference" v-show="model.documentType === 'pos'" />
                <div
                    class="ui-inline-fields"
                    v-show="model.documentType === 'pos' || model.documentType === 'creditCard'"
                >
                    <div class="field-label">{{ 'Installment count' | t }}</div>
                    <div class="field-content">
                        <ui-field
                            name="installmentCount"
                            :key="installmentCountKey"
                            :options="installmentCountOptions"
                            :field-type="model.documentType === 'creditCard' ? 'number' : 'select'"
                            label="hide"
                            :style="model.documentType === 'creditCard' ? {flex: '1 1 0'} : {}"
                            :disabled="!(model.amount > 0) || status !== 'draft'"
                        />
                        <ui-field
                            name="plusInstallmentCount"
                            :key="plusInstallmentCountKey"
                            :options="plusInstallmentCountOptions"
                            label="hide"
                            :disabled="model.installmentCount < 1 || !(model.amount > 0) || status !== 'draft'"
                            v-show="model.documentType !== 'creditCard'"
                        />
                    </div>
                </div>
                <ui-field
                    name="installmentAmount"
                    :precision="$setting('system.currencyPrecision')"
                    disabled
                    v-show="model.documentType === 'pos' || model.documentType === 'creditCard'"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="dueDifference"
                    :precision="$setting('system.currencyPrecision')"
                    disabled
                    v-show="model.documentType === 'pos'"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="total"
                    :precision="$setting('system.currencyPrecision')"
                    disabled
                    v-show="model.documentType === 'pos'"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field name="evaluateAccountCurrency" v-show="showEvaluateAccountCurrency" />
            </div>

            <div class="column is-half">
                <ui-legend title="Details" />
                <kernel-common-branch-select :disabled="status !== 'draft' || !!$params('model.partnerType')" />
                <ui-field name="recordDate" />
                <ui-field name="issueDate" />
                <ui-field name="dueDate" :disabled="status !== 'draft'" />
                <ui-field name="reference" />
                <ui-field name="description" />
                <ui-field
                    name="transactionType"
                    :options="transactionTypeOptions"
                    translate-labels
                    :disabled="status !== 'draft'"
                />
                <ui-field
                    name="countryId"
                    collection="kernel.countries"
                    :disabled="model.endorse || status !== 'draft'"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="firstWrittenBy"
                    :disabled="model.endorse || status !== 'draft'"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="financialIdentifier"
                    :disabled="model.endorse || status !== 'draft'"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="partnerBankAccountId"
                    collection="kernel.partner-bank-accounts"
                    view="partners.partners.tab-finance.bank-accounts"
                    :filters="{_id: {$in: (partner || {}).bankAccountIds || []}}"
                    :update-params="updatePartnerBankIdParams"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'promissoryNote' && !!$params('partnerId') && isReceipt"
                />
                <ui-field
                    name="guarantor1"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'promissoryNote' && isPayment"
                />
                <ui-field
                    name="guarantor2"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'promissoryNote' && isPayment"
                />
                <ui-field
                    name="issuedBy"
                    collection="kernel.partners"
                    view="partners.partners"
                    disable-create
                    disable-detail
                    :filters="{type: 'employee'}"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                />
                <ui-field
                    name="salespersonId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :filters="{type: 'employee'}"
                    :extra-fields="['code']"
                    disable-create
                    disable-detail
                    :template="'{{code}} - {{name}}'"
                    v-show="model.type === 'receipt'"
                />
                <ui-field name="scope" :options="scopeOptions" :disabled="status !== 'draft'" v-show="isScopeShown" />
                <ui-field name="tagIds" :options="tagOptions" />
                <ui-field
                    name="bankId"
                    collection="kernel.banks"
                    view="system.management.configuration.banks"
                    disable-detail
                    disable-create
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="bankBranchName"
                    :fetch-suggestions="fetchBankBranchSuggestions"
                    field-type="autocomplete"
                    :disabled="!model.bankId"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="accountNumber"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="iban"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="city"
                    :fetch-suggestions="fetchSuggestions"
                    :extra-params="{code: 'city'}"
                    field-type="autocomplete"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-field
                    name="district"
                    :fetch-suggestions="fetchSuggestions"
                    :extra-params="{code: 'district'}"
                    field-type="autocomplete"
                    v-show="model.documentType === 'cheque' || model.documentType === 'promissoryNote'"
                />
                <ui-legend title="Credit Card" class="mt30" v-show="model.documentType === 'pos'" />
                <ui-field
                    name="partnerCreditCardId"
                    collection="kernel.partner-credit-cards"
                    view="partners.partners.tab-finance.credit-cards"
                    label-from="cardHolder"
                    :filters="{_id: {$in: (partner || {}).creditCardIds || []}}"
                    :extra-fields="['cardHolder', 'cardNumber']"
                    :html-template="partnerCreditCardTemplate"
                    :update-params="updatePartnerCreditCardIdParams"
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'pos' && ((partner || {}).creditCardIds || []).length > 0"
                />
                <ui-field
                    name="cardBrand"
                    collection="finance.card-brands"
                    view="finance.configuration.card-brands"
                    value-from="code"
                    label-from="name"
                    disable-detail
                    disable-create
                    :disabled="status !== 'draft'"
                    v-show="model.documentType === 'pos' && !isSelectedCardBrandShown"
                />
                <ui-field name="cardHolder" :disabled="status !== 'draft'" v-show="model.documentType === 'pos'" />
                <ui-field name="cardNumber" mask="#### #### #### ####" v-show="model.documentType === 'pos'" />

                <div class="ui-inline-fields" v-show="model.documentType === 'pos'">
                    <div class="field-label">{{ 'Expiration date' | t }}</div>
                    <div class="field-content">
                        <ui-field
                            name="expireMonth"
                            :options="expireMonthOptions"
                            :disabled="status !== 'draft'"
                            label="hide"
                        />
                        <ui-field
                            name="expireYear"
                            :options="expireYearOptions"
                            :disabled="status !== 'draft'"
                            label="hide"
                        />
                    </div>
                </div>
                <ui-field name="cvv" :disabled="status !== 'draft'" v-show="model.documentType === 'pos'" />
            </div>
        </div>

        <ui-legend title="Guarantors" class="full-width mt30 mb0" v-show="model.documentType === 'promissoryNote'" />
        <ui-field
            name="guarantorIds"
            collection="kernel.contacts"
            view="partners.contacts.detail"
            :columns="[
                {field: 'name', label: 'Name'},
                {field: 'identity', label: 'Identity no', width: 180},
                {field: 'phone', label: 'Phone', phoneCell: true, width: 180},
                {field: 'email', label: 'Email Address', width: 240}
            ]"
            :update-params="params => ({...params, model: {type: 'contact'}, forGuarantor: true, hideTypes: true})"
            field-type="relation"
            actions="create,delete"
            :auto-height="true"
            v-show="model.documentType === 'promissoryNote'"
        />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {leadingZeros, escapeRegExp, toLower, toUpper, firstUpper} from 'framework/helpers';

export default {
    data: () => ({
        model: {},
        extraFields: [
            'id',
            'code',
            'type',
            'status',
            'no',
            'baseTotal',
            'rounding',
            'posTotal',
            'creditCardTotal',
            'currencyRate',
            'globalCurrencyRate',
            'cardNumber',
            'chequeNumber',
            'promissoryNoteNumber',
            'dueDayDifference',
            'relatedDocuments',
            'purpose',
            'overriddenPartnerAccountId',
            'workflowApprovalStatus',
            'receiptId'
        ],
        currencyFormat: null,
        systemCurrencyFormat: null,
        systemCurrencyId: null,
        paymentTerm: null,
        partner: null,
        pos: null,
        typeOptions: [
            {value: 'receipt', label: 'Receipt'},
            {value: 'payment', label: 'Payment'}
        ],
        partnerTypeOptions: [
            {value: 'customer', label: 'Customer'},
            {value: 'vendor', label: 'Vendor'},
            {value: 'employee', label: 'Employee'}
        ],
        partnerIdKey: _.uniqueId('partnerIdKey'),
        documentTypeOptions: [
            {value: 'cash', label: 'Cash'},
            {value: 'moneyTransfer', label: 'Money Transfer'},
            {value: 'cheque', label: 'Cheque'},
            {value: 'promissoryNote', label: 'Promissory note'},
            {value: 'creditCard', label: 'Credit card'},
            {value: 'pos', label: 'POS'}
        ],
        transactionTypeOptions: [
            {value: 'cheque', label: 'Cheque'},
            {value: 'moneyTransfer', label: 'Money transfer'},
            {value: 'eft', label: 'EFT'},
            {value: 'withdrawal', label: 'Withdrawal'},
            {value: 'cashDeposit', label: 'Cash deposit'},
            {value: 'fee', label: 'Fee'},
            {value: 'outgo', label: 'Outgo'},
            {value: 'advancePayment', label: 'Work advance payment'},
            {value: 'salaryAdvancePayment', label: 'Salary advance payment'},
            {value: 'promissoryNote', label: 'Promissory note'},
            {value: 'other', label: 'Other'}
        ],
        installmentCountKey: _.uniqueId('installmentCountKey_'),
        plusInstallmentCountKey: _.uniqueId('plusInstallmentCountKey_'),
        installmentCountOptions: [],
        plusInstallmentCountOptions: [],
        expireMonthOptions: _.range(0, 12).map(i => ({value: i + 1, label: leadingZeros(i + 1, 2)})),
        bank: null,
        tags: [],
        isCurrencyIdChanged: false,
        showEvaluateAccountCurrency: false,
        initialized: false
    }),

    computed: {
        type() {
            return this.model.type || this.$params('type') || 'receipt';
        },
        isReceipt() {
            return this.type === 'receipt';
        },
        isPayment() {
            return !this.isReceipt;
        },
        title() {
            const model = this.model;

            if (this.$params('id') && model.documentType) {
                const documentType = this.documentTypeOptions.find(o => o.value === model.documentType);

                if (!documentType) {
                    return '';
                }

                return this.isReceipt
                    ? `${this.$t('Receipt')} / ${this.$t(documentType.label)}`
                    : `${this.$t('Payment')} / ${this.$t(documentType.label)}`;
            } else if (model.documentType) {
                const documentType = this.documentTypeOptions.find(o => o.value === model.documentType);

                if (!documentType) {
                    return '';
                }

                return this.isReceipt
                    ? `${this.$t('New Receipt')} / ${this.$t(documentType.label)}`
                    : `${this.$t('New Payment')} / ${this.$t(documentType.label)}`;
            }

            return this.isReceipt ? this.$t('New Receipt') : this.$t('New Payment');
        },
        statuses() {
            const statuses = [{value: 'draft', label: 'Draft'}];

            if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else {
                statuses.push({value: 'approved', label: 'Approved'});
            }

            return statuses;
        },
        status() {
            const model = this.model;

            if (!model.status) {
                return 'draft';
            }

            return model.status;
        },
        assignation() {
            return {
                disabled:
                    !!this.model.workflowApprovalStatus && this.model.workflowApprovalStatus === 'waiting-for-approval'
            };
        },
        activityPayload() {
            return {
                partnerType: this.model.partnerType,
                partnerId: this.model.partnerId
            };
        },
        actions() {
            return this.$params('id') &&
                (this.isApproved ||
                    this.isCanceled ||
                    (!!this.model.workflowApprovalStatus &&
                        this.model.workflowApprovalStatus === 'waiting-for-approval'))
                ? 'edit:disabled,cancel'
                : 'edit,cancel';
        },
        extraActions() {
            const self = this;

            return [
                {
                    name: 'cancel',
                    title: 'Cancel',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    disabled() {
                        return (
                            !self.$params('id') ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                }
            ];
        },
        partnerLabel() {
            const model = this.model;

            if (model.partnerType === 'customer') return 'Customer';
            else if (model.partnerType === 'vendor') return 'Vendor';
            else if (model.partnerType === 'employee') return 'Employee';
        },
        journalIdAttributes() {
            const company = this.$store.getters['session/company'];
            const model = this.model;
            const multiCurrency = this.$setting('system.multiCurrency');
            const branchId = !!model.branchId ? model.branchId : this.$params('branchId');
            const currencyId = !!model.currencyId ? model.currencyId : this.$params('currencyId') || company.currencyId;
            const paymentType = this.isReceipt ? 'receipt' : 'payment';
            const attributes = {};
            let type = '';

            if (model.documentType === 'cash') {
                type = 'cash-safe';
            } else if (model.documentType === 'moneyTransfer') {
                type = 'bank';
            } else if (model.documentType === 'cheque') {
                type = 'cheque';
            } else if (model.documentType === 'promissoryNote') {
                type = 'promissory-note';
            }

            attributes.collection = 'accounting.journals';
            attributes.view = 'accounting.configuration.journals';
            attributes.disableDetail = true;
            attributes.disableCreate = true;

            // if (multiCurrency) {
            //     attributes.filters = {
            //         type,
            //         $or: [{currencyId: {$exists: false}}, {currencyId: ''}, {currencyId: null}, {currencyId}]
            //     };
            // } else {
            //     attributes.filters = {type};
            // }
            attributes.filters = {type};

            if (model.documentType === 'cheque') {
                attributes.filters.chequesIssued = paymentType !== 'receipt';
            } else if (model.documentType === 'promissoryNote') {
                attributes.filters.promissoryNotesIssued = paymentType !== 'receipt';
            }

            if (type === 'bank') {
                attributes.filters.$disableBranchCheck = true;
            } else {
                if (!Array.isArray(attributes.filters.$or)) attributes.filters.$or = [];

                attributes.filters.$or.push({branchId});
                attributes.filters.$or.push({branchId: {$eq: null}});
                attributes.filters.$or.push({branchId: {$exists: false}});
            }

            return attributes;
        },
        posFilters() {
            const now = this.$datetime.local().toJSDate();
            const currencyId = this.model.currencyId;
            const branchId = this.model.branchId;
            const filters = {
                $disableBranchCheck: true
            };

            filters.$and = [
                {
                    $or: [{startDate: {$exists: false}}, {startDate: null}, {startDate: {$lte: now}}]
                },
                {
                    $or: [{endDate: {$exists: false}}, {endDate: null}, {endDate: {$gte: now}}]
                },
                {
                    $or: [
                        {isVirtual: true},
                        {
                            $and: [
                                {
                                    isVirtual: false,
                                    branchId
                                },
                                {
                                    $or: [
                                        {currencyId},
                                        {currencyId: ''},
                                        {currencyId: null},
                                        {currencyId: {$exists: false}}
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ];

            return filters;
        },
        creditCardFilters() {
            const currencyId = this.model.currencyId;

            return {
                branchId: this.model.branchId,
                $or: [{currencyId}, {currencyId: ''}, {currencyId: null}, {currencyId: {$exists: false}}]
            };
        },
        isForeignCurrency() {
            const company = this.$store.getters['session/company'];
            const model = this.model;

            return model.currencyId !== company.currencyId;
        },
        paymentAccountIdFilters() {
            if (this.$setting('system.multiCurrency')) {
                return {
                    type: {$in: ['cash-safe', 'bank']},
                    $or: [
                        {currencyId: {$exists: false}},
                        {currencyId: ''},
                        {currencyId: null},
                        {currencyId: this.$params('currencyId')},
                        {currencyId: this.model.currencyId}
                    ],
                    $disableBranchCheck: true
                };
            } else {
                return {type: {$in: ['cash-safe', 'bank']}, $disableBranchCheck: true};
            }
        },
        chequeIdToEndorseFilters() {
            const query = {
                status: 'in-bill-case',
                endorsable: true
            };

            if (!!this.partner) {
                query.partnerId = {$ne: this.partner._id};
            }

            return query;
        },
        promissoryNoteIdToEndorseFilters() {
            const query = {
                status: 'in-bill-case',
                endorsable: true
            };

            if (!!this.partner) {
                query.partnerId = {$ne: this.partner._id};
            }

            return query;
        },
        cashFlowItemIdAttributes() {
            const self = this;

            const filters = {
                'tree.hasChild': {$ne: true},
                $sort: {
                    code: 1
                }
            };

            return {
                collection: 'finance.cash-flow-items',
                view: 'finance.configuration.cash-flow-items',
                filters,
                disableCreate: true,
                disableDetail: true,
                updateParams(params, type) {
                    params.isRowSelectable = row => {
                        return !row.tree.hasChild;
                    };

                    delete params.filters;

                    return params;
                },
                extraFields: ['code', 'name'],
                template: '{{code}} - {{name}}'
            };
        },
        financialProjectIdFilters() {
            return {
                $and: [
                    {
                        $or: [
                            {validFrom: {$exists: false}},
                            {validFrom: {$eq: null}},
                            {validFrom: {$lte: this.model.issueDate}}
                        ]
                    },
                    {
                        $or: [
                            {validTo: {$exists: false}},
                            {validTo: {$eq: null}},
                            {validTo: {$gte: this.model.issueDate}}
                        ]
                    }
                ],
                $sort: {code: 1}
            };
        },
        scopeOptions() {
            return [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];
        },
        isScopeShown() {
            return !!this.$setting('system.scopes') && this.model.documentType !== 'pos';
        },
        expireYearOptions() {
            const start = this.$datetime.local().year;
            const end = this.$datetime.local().year + 50;

            return _.range(start, end + 1).map(i => ({value: i, label: i}));
        },
        tagOptions() {
            let type = 'cash';

            if (this.documentType === 'moneyTransfer') type = 'money-transfer';
            else if (this.documentType === 'cheque') type = 'cheque';
            else if (this.documentType === 'promissoryNote') type = 'promissory-note';
            else if (this.documentType === 'promissoryNote') type = 'promissory-note';
            else if (this.documentType === 'pos') type = 'pos';
            else if (this.documentType === 'creditCard') type = 'credit-card';

            return this.tags
                .filter(tag => tag.scope.indexOf(type) !== -1)
                .map(tag => ({
                    value: tag._id,
                    label: tag.name
                }));
        },
        isSelectedCardBrandShown() {
            return (
                !!this.pos &&
                this.pos.isVirtual &&
                Array.isArray(this.pos.commissions) &&
                this.pos.commissions.length > 0 &&
                !this.pos.commissions.some(c => !c.cardBrand)
            );
        }
    },

    methods: {
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];
            const round = this.$app.roundNumber;
            const currencyPrecision = this.$setting('system.currencyPrecision');

            if (!this.$params('id')) {
                // Reset to defaults.
                const defaults = await this.initDefaults({
                    ...(this.$params('model') || {}),
                    documentType: model.documentType
                });
                for (const key of Object.keys(defaults)) {
                    this.model[key] = model[key] = defaults[key];
                }

                // Type.
                if (!!this.$params('type') && !model.type) {
                    model.type = this.$params('type');
                }
                if (!!this.$params('model.type') && !model.type) {
                    model.type = this.$params('model.type');
                }

                // Get default journal.
                if (!model.journalId) {
                    const query = {
                        ...this.journalIdAttributes.filters,
                        isDefault: true,
                        $select: ['_id', 'name']
                    };
                    const journal = await this.$collection('accounting.journals').findOne(query);
                    if (_.isPlainObject(journal)) {
                        model.journalId = journal._id;
                    }
                }
            } else {
                // Get currency format.
                if (!!model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId
                    });

                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            symbolPosition: currency.symbolPosition,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        }
                    };
                }

                if (!this.partner && !!model.partnerId) {
                    this.partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: [
                            'type',
                            'acceptEndorsedCheques',
                            'chequesEndorsable',
                            'acceptEndorsedPromissoryNotes',
                            'promissoryNotesEndorsable',
                            'bankAccountIds',
                            'creditCardIds',
                            'salespersonId'
                        ]
                    });

                    model.salespersonId = '';
                    if (this.partner && this.partner.salespersonId) {
                        model.salespersonId = this.partner.salespersonId;
                    }
                }

                if (model.documentType === 'pos' && !!model.posId) {
                    this.pos = await this.$collection('accounting.pos').findOne({_id: model.posId});

                    if (
                        !!this.pos &&
                        this.pos.isVirtual &&
                        Array.isArray(this.pos.commissions) &&
                        this.pos.commissions.length > 0 &&
                        !this.pos.commissions.some(c => !c.cardBrand)
                    ) {
                        if (!model.selectedCardBrand) {
                            model.selectedCardBrand = this.pos.commissions[0].cardBrand;
                        }

                        this.pos.commissions = this.pos.commissions.filter(
                            c => c.cardBrand === model.selectedCardBrand
                        );
                    }

                    if (!model.journalId) {
                        model.journalId = this.pos.journalId;
                    }

                    if (this.isSelectedCardBrandShown && !!model.selectedCardBrand) {
                        model.cardBrand = model.selectedCardBrand;
                    }

                    this.initPosInstallments();
                }
            }

            if (
                _.isArray(this.documentTypeOptions) &&
                this.documentTypeOptions.findIndex(o => o.value === model.documentType) === -1
            ) {
                model.documentType = this.documentTypeOptions[0].value;
            }

            if (!!model.bankId) {
                this.bank = await this.$collection('kernel.banks').findOne({
                    _id: model.bankId,
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
            }

            if (_.isFinite(model.amount) && _.isFinite(model.currencyRate) && model.currencyRate !== 0) {
                model.systemCurrencyTotal = round(model.amount * model.currencyRate, currencyPrecision);
            }

            return model;
        },
        async beforeValidate(model) {
            if (model.documentType === 'pos' && !model.posId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Account')}),
                    field: 'posId'
                });
            }

            if (model.documentType === 'creditCard' && !model.creditCardId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Account')}),
                    field: 'creditCardId'
                });
            }

            if (!(model.amount > 0)) {
                throw new this.$app.errors.Unprocessable('Amount must be greater than zero!');
            }

            return model;
        },
        async beforeSubmit(model) {
            const company = this.$store.getters['session/company'];

            // Currency rate.
            // if (company.currencyId !== model.currencyId && !this.$params('isPreview') && !!this.isCurrencyIdChanged) {
            //     const currency = await this.$collection('kernel.currencies').findOne({
            //         _id: model.currencyId,
            //         $select: ['name']
            //     });
            //
            //     model.currencyRate = await this.$convertCurrency({
            //         from: currency.name,
            //         to: company.currency.name,
            //         value: 1,
            //         options: {
            //             date: model.issueDate
            //         }
            //     });
            // }

            // Total.
            if (model.documentType !== 'pos') {
                model.total = model.amount;
            }
            if (model.documentType === 'pos') {
                model.posTotal = model.total;
            }
            if (model.documentType === 'creditCard') {
                model.creditCardTotal = model.total;
            }

            // Save as approved.
            if (!this.$params('id') && !!this.$params('saveAsApproved')) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('Related accounting records will be created and posted. Do you want continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;

                model.status = 'approved';
            }

            if (this.isSelectedCardBrandShown && !!model.selectedCardBrand) {
                model.cardBrand = model.selectedCardBrand;
            }

            return model;
        },
        async handleChange(model, field) {
            this.$params('loading', true);

            const company = this.$store.getters['session/company'];
            const round = this.$app.roundNumber;
            const currencyPrecision = this.$setting('system.currencyPrecision');

            console.log('field',model.systemCurrencyTotal);
            console.log('model',model.currencyRate);
            console.log('currencyPrecision',currencyPrecision);

            if (field === 'systemCurrencyTotal' && model.currencyRate !== 0) {
                this.model.amount = model.amount = round(
                    model.systemCurrencyTotal / model.currencyRate,
                    currencyPrecision
                );
            }

            if (field === 'journalId') {
                if (model.currencyId !== company.currencyId) {
                    const journal = await this.$collection('accounting.journals').findOne({
                        _id: model.journalId,
                        $select: ['_id', 'currencyId']
                    });

                    if (!!journal && (!journal.currencyId || journal.currencyId === company.currencyId)) {
                        this.showEvaluateAccountCurrency = true;
                        this.model.evaluateAccountCurrency = true;
                    } else {
                        this.showEvaluateAccountCurrency = false;
                        this.model.evaluateAccountCurrency = false;
                    }
                } else {
                    this.showEvaluateAccountCurrency = false;
                    this.model.evaluateAccountCurrency = false;
                }
            }

            if (field === 'posId') {
                if (model.currencyId !== company.currencyId) {
                    const pos = await this.$collection('accounting.pos').findOne({
                        _id: model.posId,
                        $select: ['_id', 'currencyId']
                    });

                    if (!!pos && (!pos.currencyId || pos.currencyId === company.currencyId)) {
                        this.showEvaluateAccountCurrency = true;
                        this.model.evaluateAccountCurrency = true;
                    } else {
                        this.showEvaluateAccountCurrency = false;
                        this.model.evaluateAccountCurrency = false;
                    }
                } else {
                    this.showEvaluateAccountCurrency = false;
                    this.model.evaluateAccountCurrency = false;
                }
            }

            if (field === 'currencyId') {
                if (!!model.posId) {
                    if (model.currencyId !== company.currencyId) {
                        const pos = await this.$collection('accounting.pos').findOne({
                            _id: model.posId,
                            $select: ['_id', 'currencyId']
                        });

                        if (!!pos && (!pos.currencyId || pos.currencyId === company.currencyId)) {
                            this.showEvaluateAccountCurrency = true;
                            this.model.evaluateAccountCurrency = true;
                        } else {
                            this.showEvaluateAccountCurrency = false;
                            this.model.evaluateAccountCurrency = false;
                        }
                    } else {
                        this.showEvaluateAccountCurrency = false;
                        this.model.evaluateAccountCurrency = false;
                    }
                } else if (!!model.journalId) {
                    if (model.currencyId !== company.currencyId) {
                        const journal = await this.$collection('accounting.journals').findOne({
                            _id: model.journalId,
                            $select: ['_id', 'currencyId']
                        });

                        if (!!journal && (!journal.currencyId || journal.currencyId === company.currencyId)) {
                            this.showEvaluateAccountCurrency = true;
                            this.model.evaluateAccountCurrency = true;
                        } else {
                            this.showEvaluateAccountCurrency = false;
                            this.model.evaluateAccountCurrency = false;
                        }
                    } else {
                        this.showEvaluateAccountCurrency = false;
                        this.model.evaluateAccountCurrency = false;
                    }
                } else {
                    this.showEvaluateAccountCurrency = false;
                    this.model.evaluateAccountCurrency = false;
                }
            }

            if (field === 'documentType') {
                // Reset to defaults.
                const defaults = await this.initDefaults({
                    ...(this.$params('model') || {}),
                    documentType: model.documentType,
                    branchId: model.branchId
                });
                for (const key of Object.keys(defaults)) {
                    model[key] = defaults[key];
                    this.$set(this.model, key, defaults[key]);
                }
                this.model.tags = [];

                // Get default journal.
                if (model.documentType !== 'pos' && model.documentType !== 'creditCard') {
                    const query = {
                        ...this.journalIdAttributes.filters,
                        isDefault: true,
                        $select: ['_id', 'name', 'currencyId']
                    };
                    const journal = await this.$collection('accounting.journals').findOne(query);
                    if (_.isPlainObject(journal)) {
                        this.model.journalId = journal._id;
                    } else {
                        this.model.journalId = '';
                    }
                    if (_.isPlainObject(journal) && !!journal.currencyId) {
                        model.currencyId = journal.currencyId;

                        await this.handleChange(model, 'currencyId');
                    }
                }

                // Get transaction type.
                if (model.documentType === 'moneyTransfer') {
                    this.model.transactionType = 'moneyTransfer';
                } else if (model.documentType === 'cheque') {
                    this.model.transactionType = 'cheque';
                } else {
                    this.model.transactionType = '';
                }

                // Get country.
                if (model.documentType === 'cheque' || model.documentType === 'promissoryNote') {
                    if (!model.countryId) {
                        this.model.countryId = company.address.countryId;
                    }
                } else {
                    this.model.countryId = '';
                    this.model.bankId = '';
                    this.model.bankBranchName = '';
                    this.model.iban = '';
                    this.model.accountNumber = '';
                    this.model.city = '';
                    this.model.distrcit = '';
                }
            } else if (field === 'partnerType') {
                this.$set(this.model, 'partnerId', '');

                this.$nextTick(() => {
                    this.partnerIdKey = _.uniqueId('partnerIdKey');
                });
            } else if (field === 'partnerId') {
                if (!!model.partnerId) {
                    this.partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: [
                            'type',
                            'acceptEndorsedCheques',
                            'chequesEndorsable',
                            'acceptEndorsedPromissoryNotes',
                            'promissoryNotesEndorsable',
                            'bankAccountIds',
                            'creditCardIds',
                            'name',
                            'salespersonId'
                        ]
                    });

                    if (model.original && this.partner) {
                        this.model.firstWrittenBy = this.partner.name;
                    }

                    this.model.salespersonId = '';
                    if (this.partner && this.partner.salespersonId) {
                        this.model.salespersonId = this.partner.salespersonId;
                    }
                }

                this.model.contactPersonId = '';
            } else if (field === 'branchId') {
                this.model.documentType = this.documentTypeOptions[0].value;
                this.model.scope = '1';
                this.model.journalId = '';
                this.model.partnerType = 'customer';
                this.model.partnerId = '';
                this.model.contactPersonId = '';
                this.model.documentNo = '';
                this.model.guaranteeId = '';
                this.model.description = '';
                this.model.reference = '';
                this.model.scope = '1';
                this.partner = null;

                await this.handleChange(this.model, 'documentType');
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                symbolPosition: currency.symbolPosition,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };

                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.issueDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                }

                this.isCurrencyIdChanged = true;
            } else if (field === 'creditCardId' && !!model.creditCardId) {
                const creditCardId = await this.$collection('accounting.credit-cards').findOne({
                    _id: model.creditCardId
                });

                this.model.journalId = creditCardId.journalId;
                this.model.currencyId = creditCardId.currencyId;

                await this.handleChange(this.model, 'currencyId');
            } else if (field === 'city') {
                this.model.district = '';
            }

            if (field === 'endorsable' && model.endorsable && _.isPlainObject(this.partner)) {
                if (model.documentType === 'cheque' && !this.partner.chequesEndorsable) {
                    this.$program.message(
                        'error',
                        this.$t(
                            'This partner cheques cannot be endorsed! Please update the details of relevant partner.'
                        )
                    );

                    this.model.endorsable = false;
                }

                if (model.documentType === 'promissoryNote' && !this.partner.promissoryNotesEndorsable) {
                    this.$program.message(
                        'error',
                        this.$t(
                            'This partner promissory notes cannot be endorsed! Please update the details of relevant partner.'
                        )
                    );

                    this.model.endorsable = false;
                }
            }

            if (field === 'original') {
                if (!!model.partnerId && model.original) {
                    const partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: ['name']
                    });
                    if (_.isPlainObject(partner)) this.model.firstWrittenBy = partner.name;
                } else {
                    this.model.firstWrittenBy = '';
                }
            }

            if (field === 'endorse' && model.endorse && _.isPlainObject(this.partner)) {
                if (model.documentType === 'cheque' && !this.partner.acceptEndorsedCheques) {
                    this.$program.message(
                        'error',
                        this.$t(
                            'This partner does not accept endorsed cheques! Please update the details of relevant partner.'
                        )
                    );

                    this.model.endorse = false;
                }

                if (model.documentType === 'promissoryNote' && !this.partner.acceptEndorsedPromissoryNotes) {
                    this.$program.message(
                        'error',
                        this.$t(
                            'This partner does not accept endorsed promissory notes! Please update the details of relevant partner.'
                        )
                    );

                    this.model.endorse = false;
                }
            } else if (field === 'issueDate') {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name']
                });

                if (company.currencyId !== model.currencyId) {
                    this.model.currencyRate = await this.$convertCurrency({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.issueDate
                        }
                    });
                } else {
                    this.model.currencyRate = 1;
                }
            }

            if (field === 'chequeIdToEndorse') {
                if (!!model.chequeIdToEndorse) {
                    const cheque = await this.$collection('finance.cheques').findOne({
                        _id: model.chequeIdToEndorse,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    this.model.currencyId = cheque.currencyId;
                    this.model.currencyRate = cheque.currencyRate;
                    this.model.dueDate = cheque.dueDate;
                    this.model.total = cheque.amount;
                    this.model.amount = cheque.amount;
                    this.model.journalId = cheque.journalId;
                    this.model.branchId = cheque.branchId;
                    this.model.endorsable = !!cheque.endorsable;
                    this.model.guaranteeId = cheque.guaranteeId;
                    this.model.issueDate = cheque.issueDate;
                    this.model.documentNo = cheque.chequeNumber;
                    this.model.countryId = cheque.countryId;
                    this.model.paymentAccountId = cheque.paymentAccountId;
                    this.model.endorsable = cheque.endorsable;
                    this.model.firstWrittenBy = cheque.firstWrittenBy;
                    this.model.cashFlowItemId = cheque.cashFlowItemId;
                    this.model.financialIdentifier = cheque.financialIdentifier;
                    this.model.chequeToEndorse = cheque;
                } else {
                    // Reset to defaults.
                    const defaults = await this.initDefaults({
                        ...(this.$params('model') || {}),
                        documentType: model.documentType
                    });
                    for (const key of Object.keys(defaults)) {
                        model[key] = defaults[key];
                        this.$set(this.model, key, defaults[key]);
                    }
                }

                this.isCurrencyIdChanged = true;
            }

            if (field === 'promissoryNoteIdToEndorse') {
                if (!!model.promissoryNoteIdToEndorse) {
                    const promissoryNote = await this.$collection('finance.promissory-notes').findOne({
                        _id: model.promissoryNoteIdToEndorse,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    this.model.currencyId = promissoryNote.currencyId;
                    this.model.currencyRate = promissoryNote.currencyRate;
                    this.model.dueDate = promissoryNote.dueDate;
                    this.model.total = promissoryNote.amount;
                    this.model.amount = promissoryNote.amount;
                    this.model.journalId = promissoryNote.journalId;
                    this.model.branchId = promissoryNote.branchId;
                    this.model.endorsable = !!promissoryNote.endorsable;
                    this.model.guaranteeId = promissoryNote.guaranteeId;
                    this.model.issueDate = promissoryNote.issueDate;
                    this.model.promissoryNoteNumber = promissoryNote.promissoryNoteNumber;
                    this.model.countryId = promissoryNote.countryId;
                    this.model.paymentAccountId = promissoryNote.paymentAccountId;
                    this.model.endorsable = promissoryNote.endorsable;
                    this.model.firstWrittenBy = promissoryNote.firstWrittenBy;
                    this.model.cashFlowItemId = promissoryNote.cashFlowItemId;
                    this.model.financialIdentifier = promissoryNote.financialIdentifier;
                    this.model.promissoryNoteToEndorse = promissoryNote;
                } else {
                    // Reset to defaults.
                    const defaults = await this.initDefaults({
                        ...(this.$params('model') || {}),
                        documentType: model.documentType
                    });
                    for (const key of Object.keys(defaults)) {
                        model[key] = defaults[key];
                        this.$set(this.model, key, defaults[key]);
                    }
                }

                this.isCurrencyIdChanged = true;
            }

            if (model.documentType === 'pos') {
                if (field === 'posId' && !!model.posId) {
                    this.pos = await this.$collection('accounting.pos').findOne({_id: model.posId});

                    if (
                        !!this.pos &&
                        this.pos.isVirtual &&
                        Array.isArray(this.pos.commissions) &&
                        this.pos.commissions.length > 0 &&
                        !this.pos.commissions.some(c => !c.cardBrand)
                    ) {
                        if (!model.selectedCardBrand) {
                            this.model.selectedCardBrand = model.selectedCardBrand = this.pos.commissions[0].cardBrand;
                        }

                        this.pos.commissions = this.pos.commissions.filter(
                            c => c.cardBrand === model.selectedCardBrand
                        );
                    }

                    this.model.journalId = this.pos.journalId;
                    this.model.currencyId = this.pos.currencyId;

                    await this.handleChange(this.model, 'currencyId');

                    this.initPosInstallments();

                    this.isCurrencyIdChanged = true;
                } else if (field === 'selectedCardBrand') {
                    this.pos = await this.$collection('accounting.pos').findOne({_id: model.posId});

                    if (
                        !!this.pos &&
                        this.pos.isVirtual &&
                        Array.isArray(this.pos.commissions) &&
                        this.pos.commissions.length > 0 &&
                        !this.pos.commissions.some(c => !c.cardBrand)
                    ) {
                        this.pos.commissions = this.pos.commissions.filter(
                            c => c.cardBrand === model.selectedCardBrand
                        );
                    }

                    this.initPosInstallments();
                } else if (field === 'amount') {
                    if (this.pos) {
                        const installmentCountOptions = [];
                        for (const commission of this.pos.commissions) {
                            let shouldAdd = true;

                            if (_.isNumber(commission.lowerLimit) && model.amount < commission.lowerLimit) {
                                shouldAdd = false;
                            }

                            if (_.isNumber(commission.upperLimit) && model.amount > commission.upperLimit) {
                                shouldAdd = false;
                            }

                            if (shouldAdd) {
                                installmentCountOptions.push({
                                    value: commission.installment,
                                    label: commission.installment.toString()
                                });
                            }
                        }
                        this.installmentCountOptions = installmentCountOptions;
                        this.plusInstallmentCountOptions = [];
                    }

                    if (_.isNumber(this.model.installmentCount)) {
                        await this.handleChange(this.model, 'installmentCount');
                    }
                } else if (field === 'installmentCount') {
                    const installment = this.pos.commissions.find(c => c.installment === model.installmentCount);

                    if (_.isPlainObject(installment) && _.isNumber(installment.plusInstallment)) {
                        if (installment.plusInstallment > 0) {
                            this.plusInstallmentCountOptions = [
                                {value: 0, label: ' '},
                                {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                            ];

                            if (_.isNumber(installment.plusInstallmentLowerLimit)) {
                                if (model.amount >= installment.plusInstallmentLowerLimit) {
                                    this.plusInstallmentCountOptions = [
                                        {value: 0, label: ' '},
                                        {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                                    ];
                                } else {
                                    this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                                }
                            } else {
                                this.plusInstallmentCountOptions = [
                                    {value: 0, label: ' '},
                                    {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                                ];
                            }
                        } else {
                            this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                        }
                    } else {
                        this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                    }

                    this.$set(this.model, 'plusInstallmentCount', 0);
                } else if (field === 'partnerCreditCardId' && model.partnerCreditCardId) {
                    const creditCard = await this.$collection('kernel.partner-credit-cards').findOne({
                        _id: model.partnerCreditCardId
                    });

                    if (creditCard.cardBrand) this.$set(this.model, 'cardBrand', creditCard.cardBrand);
                    if (creditCard.cardHolder) this.$set(this.model, 'cardHolder', creditCard.cardHolder);
                    if (creditCard.expireMonth) this.$set(this.model, 'expireMonth', creditCard.expireMonth);
                    if (creditCard.expireYear) this.$set(this.model, 'expireYear', creditCard.expireYear);
                    if (creditCard.cvv) this.$set(this.model, 'cvv', creditCard.cvv);
                    if (creditCard.cardNumber) this.$set(this.model, 'cardNumber', creditCard.cardNumber);
                }

                if (
                    field === 'posId' ||
                    field === 'calculateDueDifference' ||
                    field === 'installmentCount' ||
                    field === 'amount'
                ) {
                    if (model.installmentCount > 0 && _.isObject(this.pos)) {
                        const now = this.$datetime.fromJSDate(model.issueDate).startOf('day');

                        if (field !== 'dueDate') {
                            // Get due date.
                            if (this.pos.paymentOnSpecificDate) {
                                const cutoffDate = this.pos.cutoffDate;

                                if (now.day > cutoffDate) {
                                    this.model.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
                                } else {
                                    this.model.dueDate = now.set({day: cutoffDate}).toJSDate();
                                }

                                this.model.dueDate = this.$datetime
                                    .fromJSDate(this.model.dueDate)
                                    .plus({months: model.installmentCount - 1})
                                    .toJSDate();
                            } else {
                                if (this.pos.posRefund === 'lump-sum-payment') {
                                    const lumpSumPayment = this.pos.lumpSumPayments.find(
                                        c => c.installment === model.installmentCount
                                    );

                                    if (!!lumpSumPayment) {
                                        this.model.dueDate = this.$datetime
                                            .fromJSDate(model.issueDate)
                                            .plus({days: lumpSumPayment.refund || 0})
                                            .toJSDate();
                                    }
                                } else {
                                    const installmentPayment = this.pos.installmentPayments.find(
                                        c => c.installment === model.installmentCount
                                    );

                                    if (!!installmentPayment) {
                                        this.model.dueDate = this.$datetime
                                            .fromJSDate(model.issueDate)
                                            .plus({days: installmentPayment.refund || 0})
                                            .toJSDate();
                                    }
                                }
                            }
                        }

                        const dueDate = this.$datetime.fromJSDate(this.model.dueDate);
                        let dueDayDifference = dueDate.diff(now, 'days').toObject().days;
                        if (this.pos.dueType === 'average-due-difference') {
                            let newDueDaysDifference = dueDate.plus({months: 1}).diff(now, 'days').toObject().days;

                            dueDayDifference = round(newDueDaysDifference / 2);
                        }

                        const installment = this.pos.commissions.find(c => c.installment === model.installmentCount);
                        let installmentCount = model.installmentCount;
                        let installmentAmount = 0;
                        let dueDifference = 0;
                        let total = 0;

                        if (this.pos.commissionType !== 'within' && model.calculateDueDifference) {
                            dueDifference = round(
                                (model.amount * (installment.commission || 0)) / 100 +
                                    (model.amount * (installment.serviceCommission || 0)) / 100,
                                currencyPrecision
                            );
                        }

                        total = this.model.amount + dueDifference;

                        if (
                            _.isNumber(installment.interestRate) &&
                            installment.interestRate > 0 &&
                            this.pos.commissionType !== 'within' &&
                            model.calculateDueDifference
                        ) {
                            const diff = round(
                                ((total * installment.interestRate) / 3000) * dueDayDifference,
                                currencyPrecision
                            );

                            dueDifference += diff;
                            total += diff;
                        }

                        installmentAmount = round(total / installmentCount, currencyPrecision);

                        this.$set(this.model, 'installmentAmount', installmentAmount);
                        this.$set(this.model, 'dueDifference', dueDifference);
                        this.$set(this.model, 'total', total);
                    } else {
                        this.$set(this.model, 'installmentAmount', 0);
                        this.$set(this.model, 'dueDifference', 0);
                        this.$set(this.model, 'installmentAmount', 0);
                    }
                }
            }

            if (
                model.documentType === 'creditCard' &&
                (field === 'creditCardId' || field === 'installmentCount' || field === 'amount')
            ) {
                if (model.installmentCount > 0) {
                    if (field !== 'dueDate') {
                        this.model.dueDate = this.$datetime
                            .fromJSDate(model.issueDate)
                            .plus({months: model.installmentCount - 1})
                            .toJSDate();
                    }

                    this.model.installmentAmount = round(model.amount / model.installmentCount, currencyPrecision);
                }
            }

            if (model.documentType === 'cheque' && field === 'journalId' && this.type === 'payment') {
                const journal = await this.$collection('accounting.journals').findOne({
                    _id: model.journalId,
                    $select: ['_id', 'chequeBankAccountId']
                });

                if (!!journal && !!journal.chequeBankAccountId) {
                    const bankAccount = await this.$collection('accounting.bank-accounts').findOne({
                        _id: journal.chequeBankAccountId,
                        $select: ['_id', 'journalId']
                    });

                    if (!!bankAccount && !!bankAccount.journalId) {
                        this.model.paymentAccountId = bankAccount.journalId;
                    }
                }
            }

            if (field === 'bankId') {
                this.model.bankBranchName = '';
                this.bank = await this.$collection('kernel.banks').findOne({_id: model.bankId});
            }

            if (field !== 'systemCurrencyTotal') {
                this.model.systemCurrencyTotal = round(
                    (model.amount || 0) * (model.currencyRate || 1),
                    currencyPrecision
                );
            }

            this.$params('loading', false);
        },
        async handleApprove() {
            const company = this.$store.getters['session/company'];

            // Run approval workflow.
            if (this.$app.hasModule('workflow')) {
                this.$params('isPreview', true);
                this.$params('loading', true);

                try {
                    const flowResult = await this.$rpc('workflow.run-workflow', {
                        name: this.type === 'payment' ? 'finance.payments' : 'finance.receipts',
                        collectionName: 'finance.entries',
                        data: {
                            ..._.omit(this.model, 'workflowApprovalStatus')
                        },
                        id: this.$params('id'),
                        operation: 'update',
                        actionTypes: ['approval'],
                        approvalMethod: 'finance.save-entry',
                        approvalPayload: {
                            id: this.$params('id'),
                            data: {
                                ..._.omit(this.model, 'workflowApprovalStatus'),
                                status: 'approved'
                            }
                        }
                    });
                    if (!!flowResult.approvalDefinitionFound) {
                        if (flowResult.approvalIsAlreadyInProgress === true) {
                            this.$params('loading', false);
                            this.$program.alert(
                                'error',
                                this.$t(
                                    'The document is currently in the process of approval. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                                )
                            );
                            return;
                        }

                        this.$set(this.model, 'workflowApprovalStatus', 'waiting-for-approval');
                        this.$params('loading', false);
                        this.$program.alert(
                            'success',
                            this.$t(
                                'The document has been successfully submitted to the workflow approval process. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                            )
                        );
                        this.$program.emit('workflow-approval-params-changed', {
                            documentId: this.$params('id'),
                            documentCollection: 'finance.entries',
                            approvalStatus: 'waiting-for-approval'
                        });
                        return;
                    } else {
                        this.$params('loading', false);
                    }
                } catch (error) {
                    this.$program.message('error', error.message);
                    this.$params('loading', false);
                    return;
                }
            }

            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Related accounting records will be created and posted. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            if (!!this.pos && !!this.pos.isVirtual && !!this.pos.integrationType) {
                if (!this.model.documentNo) {
                    this.$program.message('error', this.$t('{{label}} is required', {label: this.$t('Document no')}));
                    return;
                }

                const payload = await new Promise(resolve => {
                    this.$program.dialog({
                        component: 'finance.components.online-pos-receipt',
                        params: {
                            posId: this.model.posId,
                            partnerId: this.model.partnerId,
                            branchId: this.model.branchId,
                            currencyId: this.model.evaluateAccountCurrency ? company.currencyId : this.model.currencyId,
                            currencyRate: this.model.evaluateAccountCurrency ? 1 : this.model.currencyRate,
                            description: this.model.description,
                            referenceId: this.$params('id'),
                            referenceCode: this.model.documentNo,
                            referenceCollection: 'finance.entries',
                            referenceView: 'finance.receivable.receipts',
                            amount: this.model.evaluateAccountCurrency
                                ? this.model.amount * this.model.currencyRate
                                : this.model.amount,
                            onSuccess: data => {
                                resolve(data);
                            }
                        },
                        onClose: () => {
                            resolve(null);
                        }
                    });
                });

                if (!_.isPlainObject(payload)) return;

                let amount = payload.amount;
                if (this.model.evaluateAccountCurrency) {
                    amount = payload.amount / this.model.currencyRate;
                }

                this.model.documentNo = payload.documentNo;
                this.model.installmentCount = payload.installmentCount;
                this.model.installmentAmount = this.$app.roundNumber(amount / payload.installmentCount, 2);
                this.model.total = amount;
                this.model.dueDifference = this.$app.roundNumber(amount - this.model.amount, 2);
                this.model.cardHolder = payload.cardHolder;
                this.model.cardBrand = payload.cardBrand;
                this.model.selectedCardBrand = payload.cardBrand;
                this.model.cardNumber = '';
                this.model.expireMonth = payload.expireMonth;
                this.model.expireYear = payload.expireYear;
                this.model.cvv = payload.cvv;
            }

            this.model.status = 'approved';
            this.$refs.view.submitForm({status: 'approved'});
        },
        async handleCancel() {
            if (this.model.documentType === 'pos') {
                this.$program.message('error', this.$t('You cannot cancel records of the POS type from entries!'));
                return;
            }

            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);
            this.$params('isPreview', true);
            try {
                await this.$rpc('finance.cancel-entries', [this.$params('id')]);

                this.$program.message('success', this.$t('Entry is canceled successfully.'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },

        async initDefaults(model) {
            const user = this.$user;
            const company = this.$store.getters['session/company'];

            if (!model.type && !!this.$params('type')) model.type = this.$params('type');
            if (!model.type && !!this.$params('model.type')) model.type = this.$params('model.type');
            if (!model.status) model.status = 'draft';

            if (!model.documentType && !!this.$params('model.documentType'))
                model.documentType = this.$params('model.documentType');
            if (!model.documentType && !!this.$params('documentType'))
                model.documentType = this.$params('documentType');
            if (!model.documentType) model.documentType = this.documentTypeOptions[0].value;
            if (!this.documentTypeOptions.some(o => o.value === model.documentType)) {
                model.documentType = this.documentTypeOptions[0].value;
            }

            if (!model.journalId && !!this.$params('journalId')) model.journalId = this.$params('journalId');
            if (!model.guaranteeId && !!this.$params('guaranteeId')) model.guaranteeId = this.$params('guaranteeId');
            if (!model.overriddenPartnerAccountId && !!this.$params('overriddenPartnerAccountId'))
                model.overriddenPartnerAccountId = this.$params('overriddenPartnerAccountId');
            if (!model.branchId)
                model.branchId = !!this.$params('branchId') ? this.$params('branchId') : user.branchIds[0];
            if (!model.currencyId)
                model.currencyId = !!this.$params('currencyId') ? this.$params('currencyId') : company.currencyId;
            if (!_.isNumber(model.currencyRate))
                model.currencyRate = !!this.$params('currencyRate') ? this.$params('currencyRate') : 1;
            if (!_.isNumber(model.globalCurrencyRate))
                model.globalCurrencyRate = !!this.$params('globalCurrencyRate')
                    ? this.$params('globalCurrencyRate')
                    : 1;
            if (!_.isDate(model.recordDate))
                model.recordDate = _.isDate(this.$params('recordDate')) ? this.$params('recordDate') : new Date();
            if (!_.isDate(model.issueDate))
                model.issueDate = _.isDate(this.$params('issueDate')) ? this.$params('issueDate') : new Date();
            if (!_.isDate(model.dueDate))
                model.dueDate = _.isDate(this.$params('dueDate')) ? this.$params('dueDate') : new Date();
            if (!model.reference) model.reference = !!this.$params('reference') ? this.$params('reference') : '';
            if (!model.description)
                model.description = !!this.$params('description') ? this.$params('description') : '';
            if (!model.issuedBy) model.issuedBy = user.partnerId;
            if (!model.scope) model.scope = '1';

            // Get transaction type.
            if (!model.transactionType) {
                if (!model.transactionType && !!this.$params('transactionType'))
                    model.transactionType = this.$params('transactionType');
                else if (!model.transactionType && !!this.$params('model.transactionType'))
                    model.transactionType = this.$params('model.transactionType');
                else if (model.documentType === 'moneyTransfer') {
                    model.transactionType = 'moneyTransfer';
                } else {
                    model.transactionType = '';
                }
            }

            // Get country.
            if (model.documentType === 'cheque' || model.documentType === 'promissoryNote') {
                if (!model.countryId) {
                    model.countryId = company.address.countryId;
                }
            } else {
                model.countryId = '';
            }

            // Get currency format.
            if (!!model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId
                });

                this.currencyFormat = {
                    currency: {
                        symbol: currency.symbol,
                        symbolPosition: currency.symbolPosition,
                        format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };
            }

            // Get partner.
            if (!model.partnerId && !!this.$params('partnerId')) {
                model.partnerId = this.$params('partnerId');
            }
            if (!this.partner && !!model.partnerId) {
                this.partner = await this.$collection('kernel.partners').findOne({
                    _id: model.partnerId,
                    $select: [
                        'type',
                        'acceptEndorsedCheques',
                        'chequesEndorsable',
                        'acceptEndorsedPromissoryNotes',
                        'promissoryNotesEndorsable',
                        'bankAccountIds',
                        'creditCardIds'
                    ]
                });

                model.partnerType = this.partner.type;
            }
            if (!!this.partner && !model.partnerType) {
                model.partnerType = this.partner.type;
            }
            if (!model.partnerType) {
                model.partnerType = this.isReceipt ? 'customer' : 'vendor';
            }

            if (model.documentType === 'pos' && !!model.posId) {
                this.pos = await this.$collection('accounting.pos').findOne({_id: model.posId});

                if (
                    !model.selectedCardBrand &&
                    !!this.pos &&
                    this.pos.isVirtual &&
                    Array.isArray(this.pos.commissions) &&
                    this.pos.commissions.length > 0 &&
                    !this.pos.commissions.some(c => !c.cardBrand)
                ) {
                    this.model.selectedCardBrand = model.selectedCardBrand = this.pos.commissions[0].cardBrand;
                    this.pos.commissions = this.pos.commissions.filter(c => c.cardBrand === model.selectedCardBrand);
                }

                if (!model.journalId) {
                    model.journalId = this.pos.journalId;
                }

                this.initPosInstallments();
            }

            return model;
        },
        initPosInstallments() {
            if (!!this.pos) {
                const installmentCountOptions = [];
                for (const commission of this.pos.commissions) {
                    let shouldAdd = true;

                    if (_.isNumber(commission.lowerLimit) && this.model.amount < commission.lowerLimit) {
                        shouldAdd = false;
                    }

                    if (_.isNumber(commission.upperLimit) && this.model.amount > commission.upperLimit) {
                        shouldAdd = false;
                    }

                    if (shouldAdd) {
                        installmentCountOptions.push({
                            value: commission.installment,
                            label: commission.installment.toString()
                        });
                    }
                }
                this.installmentCountOptions = installmentCountOptions;
                this.plusInstallmentCountOptions = [];

                const installment = this.pos.commissions.find(c => c.installment === this.model.installmentCount);

                if (_.isPlainObject(installment) && _.isNumber(installment.plusInstallment)) {
                    if (installment.plusInstallment > 0) {
                        this.plusInstallmentCountOptions = [
                            {value: 0, label: ' '},
                            {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                        ];

                        if (_.isNumber(installment.plusInstallmentLowerLimit)) {
                            if (this.model.amount >= installment.plusInstallmentLowerLimit) {
                                this.plusInstallmentCountOptions = [
                                    {value: 0, label: ' '},
                                    {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                                ];
                            } else {
                                this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                            }
                        } else {
                            this.plusInstallmentCountOptions = [
                                {value: 0, label: ' '},
                                {value: installment.plusInstallment, label: `+${installment.plusInstallment}`}
                            ];
                        }
                    } else {
                        this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                    }
                } else {
                    this.plusInstallmentCountOptions = [{value: 0, label: ' '}];
                }

                this.$nextTick(() => {
                    this.installmentCountKey = _.uniqueId('installmentCountKey_');
                    this.plusInstallmentCountKey = _.uniqueId('plusInstallmentCountKey_');
                });
            }
        },
        updatePartnerIdParams(params) {
            params.model = {type: this.model.partnerType};

            return params;
        },
        updateContactPersonIdIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {partnerId: this.model.partnerId, type: 'contact'};
            }

            return params;
        },
        updatePartnerBankIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {partnerId: (this.partner || {})._id};
                params.partnerId = (this.partner || {})._id;
            } else if (type === 'list') {
                params.filters = {_id: {$in: (this.partner || {}).bankAccountIds || []}};
            }

            return params;
        },
        partnerCreditCardTemplate(item) {
            if (item.cardNumber) {
                return `<span style="float: left">${item.cardHolder}</span><span style="float: right; color: #8492a6;">${item.cardNumber}</span>`;
            }

            return item.cardHolder;
        },
        updatePartnerCreditCardIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.partnerId = this.$params('partnerId');
            } else if (type === 'list') {
                params.filters = {_id: {$in: this.partnerCreditCardIds}};
            }

            return params;
        },
        async fetchSuggestions(search, {code: divisionCode}) {
            const model = this.model;
            const company = this.$store.getters['session/company'];

            const query = {};
            query.countryId = company.country._id;
            if (divisionCode === 'district') {
                query.city = model.city;
            }

            search = search.trim();

            if (search) {
                query.$or = [
                    {
                        [divisionCode]: {$regex: toUpper(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        [divisionCode]: {$regex: toLower(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        [divisionCode]: {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}
                    }
                ];
            }

            return await this.$rpc('kernel.component.address-suggestions', {
                query,
                divisionCode: divisionCode
            });
        },
        async fetchBankBranchSuggestions(search) {
            const query = {};

            if (_.isPlainObject(this.bank)) {
                query._id = {$in: this.bank.bankBranchIds || []};
            }

            search = search.trim();

            if (search) {
                query.$or = [
                    {
                        name: {$regex: toUpper(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        name: {$regex: toLower(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        name: {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}
                    }
                ];
            }

            query.$select = ['name'];

            return (await this.$collection('kernel.bank-branches').find(query)).map(item => item.name);
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];
        this.systemCurrencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                symbolPosition: company.currency.symbolPosition,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            }
        };
        this.systemCurrencyId = company.currencyId;

        // Get currency format.
        if (!!this.$params('currencyFormat')) {
            this.currencyFormat = this.$params('currencyFormat');

            if (!this.currencyFormat.currency.symbolPosition && !!this.$params('currencyId')) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: this.$params('currencyId')
                });

                this.currencyFormat = {
                    currency: {
                        symbol: currency.symbol,
                        symbolPosition: currency.symbolPosition,
                        format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };
            }
        } else if (!!this.$params('currencyId')) {
            const currency = await this.$collection('kernel.currencies').findOne({
                _id: this.$params('currencyId')
            });

            this.currencyFormat = {
                currency: {
                    symbol: currency.symbol,
                    symbolPosition: currency.symbolPosition,
                    format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                }
            };
        } else {
            this.currencyFormat = {
                currency: {
                    symbol: company.currency.symbol,
                    symbolPosition: company.currency.symbolPosition,
                    format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                }
            };
        }

        // Initialize payment term.
        let paymentTerm = null;
        if (_.isObject(this.$params('paymentTerm'))) {
            paymentTerm = this.$params('paymentTerm');
        } else if (this.$params('paymentTermId')) {
            paymentTerm = await this.$collection('finance.payment-terms').findOne({_id: this.$params('paymentTermId')});
        }
        this.paymentTerm = paymentTerm;

        // Get partner.
        if (this.$params('partnerId')) {
            this.partner = await this.$collection('kernel.partners').findOne({
                _id: this.$params('partnerId'),
                $select: [
                    'type',
                    'acceptEndorsedCheques',
                    'chequesEndorsable',
                    'acceptEndorsedPromissoryNotes',
                    'promissoryNotesEndorsable',
                    'bankAccountIds'
                ]
            });
        }

        // Get available types.
        if (!!this.paymentTerm) {
            const availableTypes = [];

            if (this.paymentTerm.isCashActive) availableTypes.push('cash');
            if (this.paymentTerm.isMoneyTransferActive) availableTypes.push('moneyTransfer');
            if (this.paymentTerm.isChequeActive) availableTypes.push('cheque');
            if (this.paymentTerm.isPromissoryNoteActive) availableTypes.push('promissoryNote');
            if (this.paymentTerm.isCreditCardActive && this.isPayment) availableTypes.push('creditCard');
            if (this.paymentTerm.isPosActive && this.isReceipt) availableTypes.push('pos');

            this.documentTypeOptions = this.documentTypeOptions.filter(o => availableTypes.indexOf(o.value) !== -1);
        } else {
            this.documentTypeOptions = this.documentTypeOptions
                .filter(o => !(o.value === 'pos' && this.type === 'payment'))
                .filter(o => !(o.value === 'creditCard' && this.type === 'receipt'));
        }

        // Get tag options.
        this.tags = await this.$collection('finance.tags').find({});

        // Signal initialized.
        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>
